/**
 * 第三方系统集成服务
 * 用于与迎春花质控系统等外部系统进行集成
 */

class ThirdPartyIntegrationService {
    constructor() {
        this.clientAppSDK = null
        this.isInitialized = false
        this.config = {
            appKey: '',
            appSecretKey: '',
            deptId: '',
            doctorId: '',
            qualityTarget: '2', // 1-临床, 2-病理
            linkType: '2' // 1-app, 2-web
        }
    }

    /**
     * 初始化第三方系统SDK
     * @param {Object} config - 配置参数
     * @param {string} config.sdkUrl - SDK的URL地址
     * @param {string} config.appKey - 应用密钥
     * @param {string} config.appSecretKey - 应用秘密密钥
     * @param {string} config.deptId - 科室ID
     * @param {string} config.doctorId - 医生ID
     * @param {string} config.qualityTarget - 质控目标 (1-临床, 2-病理)
     */
    async initialize(config) {
        try {
            // 更新配置
            this.config = config

            console.log("client内部配置", JSON.stringify(this.config))
            // 动态加载第三方SDK
            await this.loadSDK(config.sdkUrl)

            // 等待SDK加载完成
            await this.waitForSDK()

            // 配置SDK参数
            await this.configureSDK()

            this.isInitialized = true
            console.log('✅ 第三方系统SDK初始化成功')

            return true
        } catch (error) {
            console.error('❌ 第三方系统SDK初始化失败:', error)
            throw error
        }
    }

    /**
     * 动态加载第三方SDK脚本
     * @param {string} sdkUrl - SDK的URL地址
     */
    loadSDK(sdkUrl) {
        return new Promise((resolve, reject) => {
            // 检查是否已经加载
            if (document.querySelector(`script[src*="${sdkUrl}"]`)) {
                resolve()
                return
            }

            const script = document.createElement('script')
            script.src = `${sdkUrl}?linkType=${this.config.linkType}`
            script.onload = resolve
            script.onerror = reject
            document.head.appendChild(script)
        })
    }

    /**
     * 等待SDK加载完成
     */
    waitForSDK() {
        return new Promise((resolve, reject) => {
            let attempts = 0
            const maxAttempts = 50 // 最多等待5秒

            const checkSDK = () => {
                if (window.clientAppSDK &&  window._clientAppSDK) {
                    this.clientAppSDK = window._clientAppSDK
                    resolve()
                } else if (attempts < maxAttempts) {
                    attempts++
                    setTimeout(checkSDK, 100)
                } else {
                    reject(new Error('SDK加载超时'))
                }
            }

            checkSDK()
        })
    }

    /**
     * 配置SDK参数
     */
    async configureSDK() {
        if (!this.clientAppSDK) {
            throw new Error('SDK未初始化')
        }

        const params = {
            deptId: this.config.deptId,
            doctorId: this.config.doctorId,
            appKey: this.config.appKey,
            appSecretKey: this.config.appSecretKey,
            qualityTarget: this.config.qualityTarget
        }
        try {
            await this.clientAppSDK.changeParams(params)
            console.log('✅ SDK参数配置成功', params)
        } catch (error) {
            console.error('❌ SDK参数配置失败:', error)
            throw error
        }
    }

    /**
     * 发送患者数据到第三方系统
     * @param {Object} patientData - 患者数据，可以是原始格式或已格式化的历史数据
     */
    async sendPatientData(patientData) {
        if (!this.isInitialized) {
            throw new Error('SDK未初始化，请先调用initialize方法')
        }

        try {
            // 发送数据到第三方SDK
            await this.clientAppSDK.history(patientData)

            console.log('✅ 患者数据发送成功', JSON.stringify(patientData))
            return true
        } catch (error) {
            console.error('❌ 患者数据发送失败:', error)
            throw error
        }
    }

    /**
     * 查找第三方SDK注入的 iframe
     */
    getInjectedIframe() {
        const selectors = [
            '#_yy_iframe',
            'iframe#_yy_iframe',
            'iframe[src*="client_app_iframe"]'
        ]
        for (const sel of selectors) {
            const found = document.querySelector(sel)
            if (found) return found
        }
        return null
    }

    /**
     * 隐藏第三方SDK在页面上插入的 iframe（推荐做法，避免第三方脚本引用报错）
     */
    hideInjectedIframe() {
        try {
            const iframe = this.getInjectedIframe()
            if (iframe) {
                // 保存原始行内样式，便于恢复
                if (!iframe.dataset.prevStyle) {
                    iframe.dataset.prevStyle = iframe.getAttribute('style') || ''
                }
                iframe.style.display = 'none'
                iframe.style.visibility = 'hidden'
                iframe.style.pointerEvents = 'none'
                iframe.style.width = '0px'
                iframe.style.height = '0px'
                iframe.style.opacity = '0'
                console.log('🙈 已隐藏第三方SDK注入的 iframe')
            }
        } catch (e) {
            console.warn('隐藏第三方SDK iframe 失败:', e)
        }
    }

    /**
     * 显示第三方SDK在页面上插入的 iframe（当需要重新使用时可调用）
     */
    showInjectedIframe() {
        try {
            const iframe = this.getInjectedIframe()
            if (iframe) {
                // 恢复之前保存的行内样式
                const prev = iframe.dataset.prevStyle
                if (typeof prev === 'string') {
                    iframe.setAttribute('style', prev)
                } else {
                    iframe.style.display = ''
                    iframe.style.visibility = ''
                    iframe.style.pointerEvents = ''
                    iframe.style.width = ''
                    iframe.style.height = ''
                    iframe.style.opacity = ''
                }
                console.log('👀 已显示第三方SDK注入的 iframe')
            }
        } catch (e) {
            console.warn('显示第三方SDK iframe 失败:', e)
        }
    }


}

// 创建单例实例
const thirdPartyIntegration = new ThirdPartyIntegrationService()

export default thirdPartyIntegration
