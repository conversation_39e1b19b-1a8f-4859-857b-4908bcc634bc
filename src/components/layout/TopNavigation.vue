<template>
  <div class="top-navigation">
    <!-- 导航菜单容器 -->
    <div class="nav-menu">
      <!-- 规范诊疗智能体 -->
      <div
        v-if="!isPatientDetailPage || activeMenuId === 1"
        class="nav-item nav-item-ych"
        :class="{
          'nav-item-active': activeMenuId === 1,
          'nav-item-disabled': switchingDataSource
        }"
        @click="handleMenuClick({ id: 1, name: '规范诊疗智能体', source: 'ych' })"
      >
        <!-- 菜单内容容器 -->
        <div class="nav-content">
          <!-- 菜单图标 -->
          <img
            :src="ychIcon"
            alt="规范诊疗智能体"
            class="nav-icon"
          />
          <!-- 菜单文字 -->
          <span class="nav-text">规范诊疗智能体</span>
        </div>
        <!-- 选中状态下边框指示器 -->
        <div
          v-if="activeMenuId === 1"
          class="nav-indicator nav-indicator-ych"
        ></div>
      </div>

      <!-- 防筛智能体 -->
      <div
        v-if="!isPatientDetailPage || activeMenuId === 2"
        class="nav-item nav-item-xrk"
        :class="{
          'nav-item-active': activeMenuId === 2,
          'nav-item-disabled': switchingDataSource
        }"
        @click="handleMenuClick({ id: 2, name: '防筛智能体', source: 'xrk' })"
      >
        <!-- 菜单内容容器 -->
        <div class="nav-content">
          <!-- 菜单图标 -->
          <img
            :src="xrkIcon"
            alt="防筛智能体"
            class="nav-icon"
          />
          <!-- 菜单文字 -->
          <span class="nav-text">防筛智能体</span>
        </div>
        <!-- 选中状态下边框指示器 -->
        <div
          v-if="activeMenuId === 2"
          class="nav-indicator nav-indicator-xrk"
        ></div>
      </div>
    </div>

    <!-- 右侧按钮区域 -->
    <div class="right-buttons">
      <!-- 返回按钮 - 只在有来源页面时显示 -->
      <div v-if="showReturnButton" class="return-button" @click="handleReturn">
        <img
          src="@/assets/images/return.png"
          alt="返回"
          class="return-icon"
        />
      </div>

      <!-- 全屏按钮 -->
      <div class="fullscreen-button" @click="toggleFullscreen" :title="isFullscreen ? '退出全屏' : '进入全屏'">
        <el-icon class="fullscreen-icon">
          <FullScreen v-if="!isFullscreen" />
          <Aim v-else />
        </el-icon>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { getDataSource, hasReferrer, getDecodedReferrerUrl, removeReferrerUrl, setUrlParam } from '@/utils/urlParams'
import { useDepartmentStore, usePatientStore, useDictionaryStore } from '@/stores'
import { ElMessage } from 'element-plus'
import { FullScreen, Aim } from '@element-plus/icons-vue'

// 路由相关
const router = useRouter()
const route = useRoute()

// Store实例
const departmentStore = useDepartmentStore()
const patientStore = usePatientStore()
const dictionaryStore = useDictionaryStore()

// 导入图标
import ychIcon from '@/assets/images/ych.png'
import xrkIcon from '@/assets/images/xrk.png'

// 当前选中的菜单ID
const activeMenuId = ref(1)

// 控制返回按钮显示
const showReturnButton = ref(false)

// 全屏状态
const isFullscreen = ref(false)

// 自动全屏控制
const hasAutoFullscreenTriggered = ref(false)

// 判断是否在患者详情页面
const isPatientDetailPage = computed(() => {
  return route.name === 'PatientDetail'
})

// 数据切换加载状态
const switchingDataSource = ref(false)

// 根据数据源获取菜单ID
const getMenuIdBySource = (source) => {
  return source === 'ych' ? 1 : 2
}

// 处理菜单点击事件
const handleMenuClick = async (item) => {
  // 如果在患者详情页面，禁止切换
  if (isPatientDetailPage.value) {
    console.log('患者详情页面不允许切换数据源')

    return
  }

  // 如果点击的是当前选中的菜单项，则不执行任何操作
  if (activeMenuId.value === item.id) {
    console.log(`菜单项 ${item.name} 已经是选中状态，无需切换`)
    return
  }

  // 如果正在切换中，防止重复操作
  if (switchingDataSource.value) {
    console.log('数据源切换进行中，请稍候...')
    return
  }

  try {
    switchingDataSource.value = true
    console.log(`开始切换到菜单项: ${item.name} (${item.source})`)



    // 更新URL参数中的source
    setUrlParam('source', item.source)

    // 立即更新选中状态
    activeMenuId.value = item.id

    // 清除所有store缓存并重新加载数据
    await refreshAllData()



  } catch (error) {
    console.error('数据源切换失败:', error)
    ElMessage.error('数据源切换失败，请重试')

    // 切换失败时恢复之前的状态
    const currentSource = getDataSource()
    const currentMenuId = getMenuIdBySource(currentSource)
    activeMenuId.value = currentMenuId

  } finally {
    switchingDataSource.value = false
  }
}

// 初始化选中状态
const initializeActiveMenu = () => {
  const currentSource = getDataSource()
  const menuId = getMenuIdBySource(currentSource)
  activeMenuId.value = menuId
  console.log(`初始化菜单选中状态: ${currentSource} -> 菜单ID ${menuId}`)
}

// 初始化返回按钮显示状态
const initializeReturnButton = () => {
  showReturnButton.value = hasReferrer()
  console.log(`返回按钮显示状态: ${showReturnButton.value}`)
}

// 刷新所有数据的方法
const refreshAllData = async () => {
  try {
    console.log('开始刷新所有数据...')

    // 清除所有store缓存
    departmentStore.reset()
    patientStore.clearCache()
    dictionaryStore.clearCache() // 不传参数清除所有缓存

    // 重新获取科室数据
    await departmentStore.fetchDepartments(true)

    // 如果有选中的科室，重新获取患者数据
    if (departmentStore.selectedDepartmentId) {
      const selectedDept = departmentStore.selectedDepartment
      const departmentCode = selectedDept?.code || selectedDept?.name || departmentStore.selectedDepartmentId
      await patientStore.fetchPatientsByDepartment(departmentCode, true)
    }

    console.log('所有数据刷新完成')

  } catch (error) {
    console.error('数据刷新失败:', error)
    throw error
  }
}

// 菜单项现在支持点击切换，同时保持URL参数驱动的设计

// 处理返回按钮点击事件
const handleReturn = () => {
  console.log('返回按钮被点击')

  // 只处理返回到来源页面的功能
  if (hasReferrer()) {
    const referrerUrl = getDecodedReferrerUrl()
    console.log('检测到来源页面，准备跳转到:', referrerUrl)

    try {
      // 移除来源页面参数，避免循环跳转
      removeReferrerUrl()

      // 跳转到来源页面
      window.location.href = referrerUrl
    } catch (error) {
      console.error('跳转到来源页面失败:', error)
    }
  } else {
    console.log('没有来源页面，返回按钮无操作')
  }
}

// 全屏切换功能
const toggleFullscreen = async () => {
  try {
    if (!document.fullscreenElement) {
      // 进入全屏
      await document.documentElement.requestFullscreen()
      console.log('进入全屏模式')
    } else {
      // 退出全屏
      await document.exitFullscreen()
      console.log('退出全屏模式')
    }
  } catch (error) {
    console.error('全屏切换失败:', error)
    ElMessage.error('全屏功能不可用')
  }
}

// 监听全屏状态变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
  console.log('全屏状态变化:', isFullscreen.value ? '已进入全屏' : '已退出全屏')
  
  // 如果用户手动退出全屏，重置自动全屏标志，允许下次页面加载时再次自动全屏
  if (!isFullscreen.value && hasAutoFullscreenTriggered.value) {
    console.log('用户手动退出全屏，重置自动全屏标志')
    hasAutoFullscreenTriggered.value = false
  }
}

// 检查是否需要自动全屏
const checkAutoFullscreen = async () => {
  // 如果已经触发过自动全屏，则不再触发
  if (hasAutoFullscreenTriggered.value) {
    return
  }

  // 检查屏幕宽度
  const screenWidth = window.innerWidth
  console.log('当前屏幕宽度:', screenWidth)

  // 如果屏幕宽度小于1200px且当前不是全屏状态
  if (screenWidth < 1200 && !document.fullscreenElement) {
    console.log('屏幕宽度小于1200px，自动进入全屏模式')
    hasAutoFullscreenTriggered.value = true
    
    try {
      await document.documentElement.requestFullscreen()
      console.log('自动全屏成功')
    } catch (error) {
      console.error('自动全屏失败:', error)
    }
  }
}

// 监听路由变化，更新选中状态
watch(() => route.query.source, (newSource) => {
  if (newSource) {
    const menuId = getMenuIdBySource(newSource)
    activeMenuId.value = menuId
  }
}, { immediate: true })

// 监听路由变化，更新返回按钮显示状态
watch(() => route.query.referrer, () => {
  showReturnButton.value = hasReferrer()
  console.log(`返回按钮显示状态更新: ${showReturnButton.value}`)
}, { immediate: true })

// 组件挂载时初始化
onMounted(async () => {
  initializeActiveMenu()
  initializeReturnButton()

  // 添加全屏状态监听
  document.addEventListener('fullscreenchange', handleFullscreenChange)

  // 初始化全屏状态
  isFullscreen.value = !!document.fullscreenElement

  // 检查是否需要自动全屏
  await checkAutoFullscreen()
})

// 组件卸载时清理
onUnmounted(() => {
  // 移除全屏状态监听
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
})
</script>

<style scoped>
/* 顶部导航区域 */
.top-navigation {
  width: calc(100vw - var(--sidebar-width)); /* 减去侧边栏宽度 */
  height: var(--topnav-height);
  background-color: var(--color-bg-primary);
  border: none;

  border-radius: 0;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  padding-right: var(--spacing-xl);
  box-shadow: var(--shadow-sm);
  position: fixed; /* 固定定位 */
  top: 0;
  right: 0;
  z-index: calc(var(--z-index-dropdown) - 1); /* 确保在内容之上，但在侧边栏之下 */
}

/* 导航菜单容器 */
.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-4xl); /* 菜单项之间的间距 */
}

/* 导航菜单项 */
.nav-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer; /* 可点击光标 */
  transition: var(--transition-base);
  padding: var(--spacing-xl) 0; /* 上下内边距 */
}

/* 规范诊疗智能体菜单项 */
.nav-item-ych {
  width: 200px;
}

/* 防筛智能体菜单项 */
.nav-item-xrk {
  width: 160px;
}

/* 菜单内容容器 */
.nav-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm); /* 图标和文字之间的间距 */
  width: 100%;
  height: var(--spacing-xl);
}

/* 菜单图标样式 */
.nav-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
  flex-shrink: 0;
}

/* 菜单文字样式 - 未选中状态 */
.nav-text {
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  line-height: var(--spacing-xl);
  font-style: normal;
  text-transform: none;
  white-space: nowrap;
}

/* 选中状态的菜单文字样式 */
.nav-item-active .nav-text {
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
}

/* 下边框指示器 */
.nav-indicator {
  position: absolute;
  bottom: 0;
  height: 2px;
  background-color: var(--color-primary);
  border-radius: var(--border-radius-xs);
  animation: slideIn var(--transition-base);
}

/* 规范诊疗智能体指示器 */
.nav-indicator-ych {
  width: 200px;
  animation: slideInYch var(--transition-base);
}

/* 防筛智能体指示器 */
.nav-indicator-xrk {
  width: 160px;
  animation: slideInXrk var(--transition-base);
}

/* 规范诊疗智能体指示器动画效果 */
@keyframes slideInYch {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 200px;
    opacity: 1;
  }
}

/* 防筛智能体指示器动画效果 */
@keyframes slideInXrk {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 160px;
    opacity: 1;
  }
}

/* 禁用状态样式 */
.nav-item-disabled {
  cursor: not-allowed !important;
}

.nav-item-disabled .nav-text {
  color: var(--color-text-primary);
}

.nav-item-disabled:hover .nav-text {
  color: var(--color-text-primary);
}

/* 悬停效果 */
.nav-item:hover:not(.nav-item-active):not(.nav-item-disabled) .nav-text {
  color: var(--color-text-primary);
}

/* 右侧按钮区域 */
.right-buttons {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-left: auto; /* 推到右侧 */
}

/* 返回按钮样式 */
.return-button {
  width: 73px;
  height: var(--height-sm);
  background: var(--color-bg-primary);
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-base);
}

/* 返回按钮图标 */
.return-icon {
  object-fit: contain;
}

/* 返回按钮悬停效果 */
.return-button:hover {
  transform: translateY(-1px);
}

/* 返回按钮点击效果 */
.return-button:active {
  transform: translateY(0);
}

/* 全屏按钮样式 */
.fullscreen-button {
  width: var(--height-sm);
  height: var(--height-sm);
  background: var(--color-bg-primary);
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-base);
  border: 1px solid var(--color-border-light);
}

/* 全屏按钮图标 */
.fullscreen-icon {
  font-size: 16px;
  color: var(--color-text-secondary);
  transition: var(--transition-base);
}

/* 全屏按钮悬停效果 */
.fullscreen-button:hover {
  transform: translateY(-1px);
  border-color: var(--color-primary);
}

.fullscreen-button:hover .fullscreen-icon {
  color: var(--color-primary);
}

/* 全屏按钮点击效果 */
.fullscreen-button:active {
  transform: translateY(0);
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .top-navigation {
    padding: 0 var(--spacing-lg);
    height: var(--topnav-height-mobile);
    width: 100%; /* 移动端占满宽度 */
    position: relative; /* 移动端取消固定定位 */
  }

  .nav-menu {
    gap: var(--spacing-xl);
  }

  .nav-item {
    padding: var(--spacing-sm) 0;
  }

  .nav-item-ych {
    width: 140px;
  }

  .nav-item-xrk {
    width: 120px;
  }

  .nav-content {
    gap: var(--spacing-xs);
  }

  .nav-icon {
    width: 16px;
    height: 16px;
  }

  .nav-text {
    font-size: var(--font-size-md);
  }

  .nav-indicator-ych {
    width: 140px;
  }

  .nav-indicator-xrk {
    width: 120px;
  }

  /* 移动端按钮适配 */
  .right-buttons {
    gap: var(--spacing-sm);
  }

  .return-button {
    width: 60px;
    height: var(--height-xs);
  }

  .return-icon {
    width: var(--spacing-sm);
    height: var(--spacing-sm);
  }

  .fullscreen-button {
    width: var(--height-xs);
    height: var(--height-xs);
  }

  .fullscreen-icon {
    font-size: 14px;
  }

  @keyframes slideInYch {
    from {
      width: 0;
      opacity: 0;
    }
    to {
      width: 140px;
      opacity: 1;
    }
  }

  @keyframes slideInXrk {
    from {
      width: 0;
      opacity: 0;
    }
    to {
      width: 120px;
      opacity: 1;
    }
  }
}

/* 平板适配 */
@media screen and (min-width: 769px) and (max-width: 1024px) {
  .nav-menu {
    gap: var(--spacing-3xl);
  }

  .nav-item-ych {
    width: 180px;
  }

  .nav-item-xrk {
    width: 140px;
  }

  .nav-icon {
    width: 18px;
    height: 18px;
  }

  .nav-text {
    font-size: 17px;
  }

  .nav-indicator-ych {
    width: 180px;
  }

  .nav-indicator-xrk {
    width: 140px;
  }

  /* 平板端按钮适配 */
  .right-buttons {
    gap: var(--spacing-sm);
  }

  .return-button {
    width: 68px;
    height: 30px;
  }

  .return-icon {
    width: 15px;
    height: 15px;
  }

  .fullscreen-button {
    width: 30px;
    height: 30px;
  }

  .fullscreen-icon {
    font-size: 15px;
  }
}
</style>
