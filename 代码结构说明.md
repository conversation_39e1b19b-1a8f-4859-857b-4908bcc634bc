# YYHIS-WEB 代码结构说明

## 项目结构

```
yyhis-web/
├── public/                     # 静态资源目录
│   ├── config.js              # 第三方配置文件
│   ├── images/                # 图片资源
│   └── 来源页面返回测试.html    # 测试页面
├── src/                       # 源代码目录
│   ├── api/                   # API 接口层
│   │   ├── endpoints.js       # API 端点配置
│   │   ├── index.js          # API 统一导出
│   │   └── services/         # 业务服务层
│   │       ├── patientService.js        # 患者服务
│   │       ├── departmentService.js     # 科室服务
│   │       ├── admissionRecordService.js # 入院记录服务
│   │       ├── consultationRecordService.js # 门诊记录服务
│   │       ├── surgeryRecordService.js  # 手术记录服务
│   │       ├── labReportService.js      # 检验报告服务
│   │       ├── examReportService.js     # 检查报告服务
│   │       ├── pathologyReportService.js # 病理报告服务
│   │       ├── medicalOrderService.js   # 医嘱服务
│   │       ├── diagnosisInfoService.js  # 诊断信息服务
│   │       ├── dictionaryService.js     # 字典服务
│   │       └── thirdPartyIntegrationService.js # 第三方集成服务
│   ├── assets/               # 静态资源
│   │   ├── images/          # 图片文件
│   │   └── vue.svg         # Vue 图标
│   ├── components/          # 组件目录
│   │   ├── business/        # 业务组件
│   │   │   ├── PatientCard.vue          # 患者卡片
│   │   │   ├── PatientCardList.vue      # 患者列表
│   │   │   ├── PatientInfoPanel.vue     # 患者信息面板
│   │   │   ├── DepartmentItem.vue       # 科室项目
│   │   │   ├── DepartmentMenu.vue       # 科室菜单
│   │   │   ├── MedicalRecordMenu.vue    # 病历菜单
│   │   │   ├── AdmissionRecord.vue      # 入院记录
│   │   │   ├── ConsultationRecord.vue   # 门诊记录
│   │   │   ├── DischargeRecord.vue      # 出院记录
│   │   │   ├── SurgeryRecord.vue        # 手术记录
│   │   │   ├── FirstCourseRecord.vue    # 首次病程记录
│   │   │   ├── DailyProgressRecord.vue  # 日常病程记录
│   │   │   ├── PostoperativeFirstCourseRecord.vue # 术后首次病程
│   │   │   ├── PreoperativeDiscussion.vue # 术前讨论
│   │   │   ├── PreoperativeSummary.vue  # 术前小结
│   │   │   ├── LabReport.vue           # 检验报告
│   │   │   ├── ExamReport.vue          # 检查报告
│   │   │   ├── PathologyReport.vue     # 病理报告
│   │   │   ├── MolecularPathologyReport.vue # 分子病理报告
│   │   │   ├── DiagnosisInfo.vue       # 诊断信息
│   │   │   ├── DiagnosisSelector.vue   # 诊断选择器
│   │   │   ├── MedicalOrderInfo.vue    # 医嘱信息
│   │   │   ├── InvasiveProcedure.vue   # 有创操作
│   │   │   ├── TnmStagingRecord.vue    # TNM分期记录
│   │   │   └── LogoSection.vue         # Logo 区域
│   │   ├── common/          # 通用组件
│   │   │   ├── PatientAvatar.vue       # 患者头像
│   │   │   ├── LoadingState.vue        # 加载状态
│   │   │   └── ErrorState.vue          # 错误状态
│   │   ├── layout/          # 布局组件
│   │   │   ├── TopNavigation.vue       # 顶部导航
│   │   │   ├── Sidebar.vue             # 侧边栏
│   │   │   ├── SecondaryNavigation.vue # 二级导航
│   │   │   └── Footer.vue              # 页脚
│   │   ├── test/            # 测试组件
│   │   └── ApiCallTracker.vue # API 调用跟踪器
│   ├── config/              # 配置文件
│   │   ├── index.js         # 主配置文件
│   │   └── thirdPartyConfig.js # 第三方配置
│   ├── layouts/             # 页面布局
│   │   ├── DefaultLayout.vue        # 默认布局
│   │   └── PatientDetailLayout.vue  # 患者详情布局
│   ├── router/              # 路由配置
│   │   ├── index.js         # 路由主文件
│   │   └── routes/          # 路由模块
│   │       └── medical.js   # 医疗模块路由
│   ├── services/            # 服务层
│   │   └── thirdPartyIntegration.js # 第三方集成服务
│   ├── stores/              # 状态管理
│   │   ├── index.js         # Store 统一导出
│   │   ├── patientStore.js  # 患者状态管理
│   │   ├── departmentStore.js # 科室状态管理
│   │   └── dictionaryStore.js # 字典状态管理
│   ├── styles/              # 样式文件
│   │   ├── index.css        # 主样式文件
│   │   ├── common.css       # 通用样式
│   │   ├── variables.css    # CSS 变量
│   │   └── responsive.css   # 响应式样式
│   ├── utils/               # 工具函数
│   │   ├── request.js       # HTTP 请求封装
│   │   ├── urlParams.js     # URL 参数处理
│   │   ├── patientUtils.js  # 患者相关工具
│   │   ├── patientDataAdapter.js # 患者数据适配器
│   │   ├── avatarGenerator.js # 头像生成器
│   │   ├── corsHelper.js    # CORS 辅助工具
│   │   ├── responsiveTest.js # 响应式测试
│   │   └── verifyAvatars.js # 头像验证
│   ├── views/               # 页面视图
│   │   ├── Home.vue         # 首页
│   │   ├── integration/     # 集成相关页面
│   │   └── medical/         # 医疗模块页面
│   │       ├── DepartmentList.vue    # 科室列表
│   │       ├── PatientManagement.vue # 患者管理
│   │       └── PatientDetail.vue     # 患者详情
│   ├── App.vue              # 根组件
│   └── main.js              # 应用入口
├── scripts/                 # 构建脚本
│   ├── build-info.js        # 构建信息生成
│   └── compare-envs.js      # 环境比较工具
├── docker/                  # Docker 配置
│   └── Dockerfile           # Docker 构建文件
├── docs/                    # 文档目录
│   └── 集成文档.md          # 集成说明文档
├── config-examples/         # 配置示例
├── dist/                    # 构建输出目录
├── vite.config.js           # Vite 配置文件
├── package.json             # 项目依赖配置
└── README.md                # 项目说明

